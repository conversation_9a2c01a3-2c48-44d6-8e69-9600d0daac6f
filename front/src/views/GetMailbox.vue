<template>
  <div class="get-mailbox-page">
    <div class="page-background">
      <div class="bg-pattern"></div>
      <div class="bg-gradient"></div>
    </div>

    <div class="container">
      <div class="header-section">
        <div class="title-wrapper">
          <div class="title-icon">
            📧
          </div>
          <h1 class="page-title">临时邮箱</h1>
        </div>
        <p class="page-description">
          安全、快速、免费的临时邮箱服务，保护您的隐私
        </p>
      </div>

      <div class="content-section">
        <!-- 邮箱生成区域 -->
        <div class="generator-panel">
          <div class="panel-header">
            <div class="header-icon">
              ➕
            </div>
            <div class="header-content">
              <h2 class="panel-title">创建邮箱</h2>
              <p class="panel-subtitle">选择域名并生成您的临时邮箱</p>
            </div>
          </div>

          <div class="generator-content">
            <!-- 域名选择 -->
            <div class="domain-section">
              <label class="section-label">
                <t-icon name="link" />
                选择域名
              </label>
              <div class="domain-grid">
                <div
                  v-for="domain in availableDomains"
                  :key="domain.value"
                  class="domain-card"
                  :class="{ active: selectedDomain === domain.value }"
                  @click="selectedDomain = domain.value"
                >
                  <div class="domain-info">
                    <span class="domain-name">{{ domain.label }}</span>
                    <t-tag v-if="domain.hot" theme="danger" size="small" class="hot-badge">
                      <t-icon name="fire" size="12px" />
                      热门
                    </t-tag>
                  </div>
                  <div class="domain-check">
                    <t-icon name="check" v-if="selectedDomain === domain.value" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 前缀输入 -->
            <div class="prefix-section">
              <label class="section-label">
                <t-icon name="edit" />
                邮箱前缀
              </label>
              <div class="prefix-input-wrapper">
                <div class="input-group">
                  <t-input
                    v-model="emailPrefix"
                    placeholder="输入自定义前缀"
                    class="prefix-input"
                    size="large"
                  />
                  <t-button
                    variant="outline"
                    @click="generateRandomPrefix"
                    class="random-btn"
                    size="large"
                  >
                    <t-icon name="refresh" />
                  </t-button>
                </div>
                <div class="email-preview-card">
                  <div class="preview-header">
                    <t-icon name="mail" />
                    <span>邮箱预览</span>
                  </div>
                  <div class="preview-email">{{ emailPrefix || 'example' }}@{{ selectedDomain }}</div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
              <t-button
                theme="primary"
                size="large"
                @click="addToMyMailbox"
                :disabled="!emailPrefix"
                class="create-btn"
              >
                <t-icon name="check-circle" />
                创建并使用此邮箱
              </t-button>
            </div>
          </div>
        </div>

        <!-- 邮件收信箱 -->
        <div class="inbox-panel">
          <div class="panel-header">
            <div class="header-icon">
              <t-icon name="inbox" />
            </div>
            <div class="header-content">
              <h2 class="panel-title">收信箱</h2>
              <p class="panel-subtitle">实时接收邮件和验证码</p>
            </div>
            <div class="header-actions">
              <t-button size="small" variant="text" @click="refreshInbox" class="action-btn">
                <t-icon name="refresh" />
              </t-button>
              <t-button size="small" variant="text" @click="clearInbox" class="action-btn">
                <t-icon name="delete" />
              </t-button>
            </div>
          </div>

          <div class="inbox-content">
            <div v-if="emails.length === 0" class="empty-state">
              <div class="empty-icon">
                <t-icon name="mail" />
              </div>
              <h3 class="empty-title">收信箱为空</h3>
              <p class="empty-desc">创建邮箱后，邮件将自动显示在这里</p>
            </div>

            <div v-else class="email-list">
              <div
                v-for="email in emails"
                :key="email.id"
                class="email-card"
                @click="selectEmail(email)"
                :class="{ active: selectedEmail?.id === email.id }"
              >
                <div class="email-avatar">
                  <t-icon name="user" />
                </div>
                <div class="email-content">
                  <div class="email-meta">
                    <span class="email-sender">{{ email.sender }}</span>
                    <span class="email-time">{{ formatTime(email.time) }}</span>
                  </div>
                  <h4 class="email-subject">{{ email.subject }}</h4>
                  <p class="email-preview">{{ getEmailPreview(email.content) }}</p>
                  <div v-if="email.verificationCode" class="verification-badge">
                    <t-icon name="lock" />
                    <span>验证码：{{ email.verificationCode }}</span>
                  </div>
                </div>
                <div class="email-indicator">
                  <div class="unread-dot" v-if="!email.read"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()

// 可用域名列表
const availableDomains = ref([
  { label: 'tempmail.com', value: 'tempmail.com', hot: true },
  { label: '10minutemail.com', value: '10minutemail.com', hot: true },
  { label: 'guerrillamail.com', value: 'guerrillamail.com' },
  { label: 'mailinator.com', value: 'mailinator.com' },
  { label: 'temp-mail.org', value: 'temp-mail.org' },
  { label: 'throwaway.email', value: 'throwaway.email' }
])

// 选中的域名
const selectedDomain = ref('tempmail.com')

// 邮箱前缀
const emailPrefix = ref('')

// 邮件列表
const emails = ref([])

// 选中的邮件
const selectedEmail = ref(null)

// Mock邮件数据
const mockEmails = [
  {
    id: 1,
    sender: '<EMAIL>',
    subject: 'GitHub 验证码',
    content: '您的 GitHub 验证码是：123456。此验证码将在10分钟后过期，请及时使用。如果您没有请求此验证码，请忽略此邮件。为了保护您的账户安全，请不要将此验证码分享给任何人。',
    time: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
    verificationCode: '123456',
    read: false
  },
  {
    id: 2,
    sender: '<EMAIL>',
    subject: 'Google 账户安全验证',
    content: '您好，我们检测到您的 Google 账户有新的登录活动。验证码：789012。为了保护您的账户安全，请确认这是您本人的操作。如果不是您的操作，请立即更改密码并启用两步验证。',
    time: new Date(Date.now() - 15 * 60 * 1000), // 15分钟前
    verificationCode: '789012',
    read: false
  },
  {
    id: 3,
    sender: '<EMAIL>',
    subject: '亚马逊订单确认',
    content: '感谢您在亚马逊购物！您的订单 #123-4567890-1234567 已确认。预计送达时间：2024年1月15日。您可以在我的订单页面查看详细信息和物流状态。如有任何问题，请联系客服。',
    time: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
    verificationCode: null,
    read: true
  },
  {
    id: 4,
    sender: '<EMAIL>',
    subject: 'Twitter 登录验证',
    content: '您的 Twitter 登录验证码是：456789。请在5分钟内输入此验证码完成登录。如果这不是您的操作，请立即更改密码。我们建议您启用两步验证以提高账户安全性。',
    time: new Date(Date.now() - 45 * 60 * 1000), // 45分钟前
    verificationCode: '456789',
    read: true
  },
  {
    id: 5,
    sender: '<EMAIL>',
    subject: 'Microsoft 账户登录通知',
    content: '我们检测到您的 Microsoft 账户在新设备上登录。如果这是您的操作，请忽略此邮件。如果不是，请立即访问账户安全页面更改密码。登录时间：2024年1月10日 14:30，设备：Windows PC。',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
    verificationCode: null,
    read: true
  }
]

// 生成随机前缀
const generateRandomPrefix = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  emailPrefix.value = result
}

// 添加到我的邮箱
const addToMyMailbox = () => {
  if (!emailPrefix.value) {
    MessagePlugin.warning('请输入邮箱前缀')
    return
  }

  const fullEmail = `${emailPrefix.value}@${selectedDomain.value}`
  MessagePlugin.success(`邮箱 ${fullEmail} 已添加到您的账户`)

  // 模拟添加邮件到收信箱
  setTimeout(() => {
    const newEmail = {
      id: emails.value.length + 1,
      sender: 'welcome@' + selectedDomain.value,
      subject: '欢迎使用临时邮箱服务',
      content: `欢迎使用 ${fullEmail}！您的临时邮箱已激活，可以开始接收邮件了。此邮箱将保持活跃状态，直到您手动删除。您可以使用此邮箱接收验证码、注册确认邮件等。`,
      time: new Date(),
      verificationCode: null,
      read: false
    }
    emails.value.unshift(newEmail)
  }, 1000)
}

// 刷新收信箱
const refreshInbox = () => {
  MessagePlugin.info('正在刷新收信箱...')

  // 模拟刷新，随机添加新邮件
  setTimeout(() => {
    if (Math.random() > 0.3) {
      const senders = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]
      const subjects = [
        'Facebook 登录验证',
        'Apple ID 安全提醒',
        'LinkedIn 连接请求',
        'PayPal 交易确认',
        'Instagram 新关注者'
      ]
      const randomSender = senders[Math.floor(Math.random() * senders.length)]
      const randomSubject = subjects[Math.floor(Math.random() * subjects.length)]

      const newEmail = {
        id: emails.value.length + 1,
        sender: randomSender,
        subject: randomSubject,
        content: '这是一封新收到的邮件，用于演示刷新功能。邮件内容会根据实际情况显示，包含重要的通知信息。',
        time: new Date(),
        verificationCode: Math.random() > 0.6 ? Math.floor(Math.random() * 900000 + 100000).toString() : null,
        read: false
      }
      emails.value.unshift(newEmail)
      MessagePlugin.success('收到新邮件')
    } else {
      MessagePlugin.info('暂无新邮件')
    }
  }, 1000)
}

// 清空收信箱
const clearInbox = () => {
  emails.value = []
  selectedEmail.value = null
  MessagePlugin.success('收信箱已清空')
}

// 选择邮件
const selectEmail = (email) => {
  selectedEmail.value = email
  // 标记为已读
  email.read = true
}

// 格式化时间
const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return time.toLocaleDateString('zh-CN')
}

// 获取邮件预览
const getEmailPreview = (content) => {
  if (content.length <= 50) return content
  return content.substring(0, 50) + '...'
}

// 组件挂载时初始化数据
onMounted(() => {
  // 加载mock邮件数据
  emails.value = [...mockEmails]

  // 生成初始前缀
  generateRandomPrefix()
})
</script>

<style lang="less" scoped>
.get-mailbox-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.page-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  background-size: 800px 800px;
  animation: float 20s ease-in-out infinite;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.95) 50%,
    rgba(241, 245, 249, 0.9) 100%);
  backdrop-filter: blur(10px);
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 1;

  @media (min-width: 768px) {
    padding: 60px 40px;
  }
}

.header-section {
  text-align: center;
  margin-bottom: 60px;
}

.title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.title-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.page-title {
  font-size: 48px;
  font-weight: 800;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.02em;

  @media (min-width: 768px) {
    font-size: 56px;
  }
}

.page-description {
  font-size: 20px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 400;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;

  @media (min-width: 1200px) {
    grid-template-columns: 480px 1fr;
    gap: 40px;
  }
}

// 面板通用样式
.generator-panel,
.inbox-panel {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.15),
      0 20px 20px -5px rgba(0, 0, 0, 0.08);
  }
}

.panel-header {
  padding: 32px 32px 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 20px;
}

.header-content {
  flex: 1;
}

.panel-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 4px 0;
  letter-spacing: -0.01em;
}

.panel-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(229, 231, 235, 0.3);
  color: #6b7280;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    color: #374151;
    transform: scale(1.05);
  }
}

.generator-content {
  padding: 32px;
}

.section-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.domain-section {
  margin-bottom: 32px;
}

.domain-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }
}

.domain-card {
  padding: 20px;
  border: 2px solid rgba(229, 231, 235, 0.5);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-1px);
  }

  &.active {
    border-color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }
}

.domain-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.domain-name {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.hot-badge {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  font-weight: 600;
}

.domain-check {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.2s;

  .domain-card.active & {
    opacity: 1;
  }
}

.prefix-section {
  margin-bottom: 32px;
}

.prefix-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-group {
  display: flex;
  gap: 12px;
}

.prefix-input {
  flex: 1;
  border-radius: 12px;
  border: 2px solid rgba(229, 231, 235, 0.5);
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

.random-btn {
  border-radius: 12px;
  border: 2px solid rgba(229, 231, 235, 0.5);
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
    color: #3b82f6;
    transform: scale(1.02);
  }
}

.email-preview-card {
  padding: 20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.02) 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
}

.preview-email {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: -0.01em;
}

.action-section {
  margin-top: 32px;
}

.create-btn {
  border-radius: 16px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 收信箱样式
.inbox-content {
  padding: 0;
}

.empty-state {
  text-align: center;
  padding: 80px 32px;
  color: #6b7280;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 32px;
  color: #9ca3af;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-desc {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.email-list {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.email-card {
  padding: 24px;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(229, 231, 235, 0.3);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  gap: 16px;
  position: relative;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  }

  &.active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }
}

.email-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 18px;
  flex-shrink: 0;
}

.email-content {
  flex: 1;
  min-width: 0;
}

.email-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.email-sender {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.email-time {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.email-subject {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.email-preview {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 12px;
}

.verification-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.email-indicator {
  display: flex;
  align-items: flex-start;
  padding-top: 4px;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
