<template>
  <div class="get-mailbox-page">
    <div class="container">
      <div class="header-section">
        <h1 class="page-title">获取邮箱</h1>
        <p class="page-description">
          快速获取临时邮箱，接收验证码和邮件
        </p>
      </div>

      <div class="content-section">
        <!-- 邮箱生成区域 -->
        <t-card class="mailbox-generator-card">
          <template #header>
            <h2>生成临时邮箱</h2>
          </template>

          <div class="generator-content">
            <!-- 域名选择 -->
            <div class="domain-section">
              <label class="section-label">选择域名</label>
              <t-radio-group v-model="selectedDomain" class="domain-list">
                <t-radio
                  v-for="domain in availableDomains"
                  :key="domain.value"
                  :value="domain.value"
                  class="domain-option"
                >
                  {{ domain.label }}
                  <t-tag v-if="domain.hot" theme="danger" size="small" class="hot-tag">热门</t-tag>
                </t-radio>
              </t-radio-group>
            </div>

            <!-- 前缀输入 -->
            <div class="prefix-section">
              <label class="section-label">邮箱前缀</label>
              <div class="prefix-input-group">
                <t-input
                  v-model="emailPrefix"
                  placeholder="输入前缀或点击随机生成"
                  class="prefix-input"
                />
                <t-button
                  variant="outline"
                  @click="generateRandomPrefix"
                  class="random-btn"
                >
                  <t-icon name="refresh" />
                  随机生成
                </t-button>
              </div>
              <div class="email-preview">
                <span class="preview-label">预览：</span>
                <span class="preview-email">{{ emailPrefix || 'example' }}@{{ selectedDomain }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
              <t-button
                theme="primary"
                size="large"
                @click="addToMyMailbox"
                :disabled="!emailPrefix"
                block
              >
                <t-icon name="add" />
                添加到我的邮箱
              </t-button>
            </div>
          </div>
        </t-card>

        <!-- 邮件收信箱 -->
        <t-card class="inbox-card">
          <template #header>
            <div class="inbox-header">
              <h2>邮件收信箱</h2>
              <div class="inbox-actions">
                <t-button size="small" variant="outline" @click="refreshInbox">
                  <t-icon name="refresh" />
                  刷新
                </t-button>
                <t-button size="small" variant="outline" @click="clearInbox">
                  <t-icon name="delete" />
                  清空
                </t-button>
              </div>
            </div>
          </template>

          <div class="inbox-content">
            <div v-if="emails.length === 0" class="empty-inbox">
              <t-icon name="mail" size="48px" class="empty-icon" />
              <p class="empty-text">暂无邮件</p>
              <p class="empty-desc">邮件将自动显示在这里</p>
            </div>

            <div v-else class="email-list">
              <div
                v-for="email in emails"
                :key="email.id"
                class="email-item"
                @click="selectEmail(email)"
                :class="{ active: selectedEmail?.id === email.id }"
              >
                <div class="email-header">
                  <div class="email-sender">
                    <t-icon name="user" />
                    <span>{{ email.sender }}</span>
                  </div>
                  <div class="email-time">{{ formatTime(email.time) }}</div>
                </div>
                <div class="email-subject">{{ email.subject }}</div>
                <div class="email-preview">{{ getEmailPreview(email.content) }}</div>
                <div v-if="email.verificationCode" class="verification-code">
                  <t-tag theme="primary" size="small">
                    验证码：{{ email.verificationCode }}
                  </t-tag>
                </div>
              </div>
            </div>
          </div>
        </t-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()

// 可用域名列表
const availableDomains = ref([
  { label: 'tempmail.com', value: 'tempmail.com', hot: true },
  { label: '10minutemail.com', value: '10minutemail.com', hot: true },
  { label: 'guerrillamail.com', value: 'guerrillamail.com' },
  { label: 'mailinator.com', value: 'mailinator.com' },
  { label: 'temp-mail.org', value: 'temp-mail.org' },
  { label: 'throwaway.email', value: 'throwaway.email' }
])

// 选中的域名
const selectedDomain = ref('tempmail.com')

// 邮箱前缀
const emailPrefix = ref('')

// 邮件列表
const emails = ref([])

// 选中的邮件
const selectedEmail = ref(null)

// Mock邮件数据
const mockEmails = [
  {
    id: 1,
    sender: '<EMAIL>',
    subject: 'GitHub 验证码',
    content: '您的 GitHub 验证码是：123456。此验证码将在10分钟后过期，请及时使用。如果您没有请求此验证码，请忽略此邮件。',
    time: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
    verificationCode: '123456'
  },
  {
    id: 2,
    sender: '<EMAIL>',
    subject: 'Google 账户安全验证',
    content: '您好，我们检测到您的 Google 账户有新的登录活动。验证码：789012。为了保护您的账户安全，请确认这是您本人的操作。',
    time: new Date(Date.now() - 15 * 60 * 1000), // 15分钟前
    verificationCode: '789012'
  },
  {
    id: 3,
    sender: '<EMAIL>',
    subject: '亚马逊订单确认',
    content: '感谢您在亚马逊购物！您的订单 #123-4567890-1234567 已确认。预计送达时间：2024年1月15日。如有任何问题，请联系客服。',
    time: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
    verificationCode: null
  },
  {
    id: 4,
    sender: '<EMAIL>',
    subject: 'Twitter 登录验证',
    content: '您的 Twitter 登录验证码是：456789。请在5分钟内输入此验证码完成登录。如果这不是您的操作，请立即更改密码。',
    time: new Date(Date.now() - 45 * 60 * 1000), // 45分钟前
    verificationCode: '456789'
  }
]

// 生成随机前缀
const generateRandomPrefix = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  emailPrefix.value = result
}

// 添加到我的邮箱
const addToMyMailbox = () => {
  if (!emailPrefix.value) {
    MessagePlugin.warning('请输入邮箱前缀')
    return
  }

  const fullEmail = `${emailPrefix.value}@${selectedDomain.value}`
  MessagePlugin.success(`邮箱 ${fullEmail} 已添加到您的账户`)

  // 模拟添加邮件到收信箱
  setTimeout(() => {
    const newEmail = {
      id: emails.value.length + 1,
      sender: 'welcome@' + selectedDomain.value,
      subject: '欢迎使用临时邮箱服务',
      content: `欢迎使用 ${fullEmail}！您的临时邮箱已激活，可以开始接收邮件了。此邮箱将保持活跃状态，直到您手动删除。`,
      time: new Date(),
      verificationCode: null
    }
    emails.value.unshift(newEmail)
  }, 1000)
}

// 刷新收信箱
const refreshInbox = () => {
  MessagePlugin.info('正在刷新收信箱...')

  // 模拟刷新，随机添加新邮件
  setTimeout(() => {
    if (Math.random() > 0.5) {
      const newEmail = {
        id: emails.value.length + 1,
        sender: '<EMAIL>',
        subject: '新邮件通知',
        content: '这是一封新收到的邮件，用于演示刷新功能。邮件内容会根据实际情况显示。',
        time: new Date(),
        verificationCode: Math.random() > 0.5 ? Math.floor(Math.random() * 900000 + 100000).toString() : null
      }
      emails.value.unshift(newEmail)
      MessagePlugin.success('收到新邮件')
    } else {
      MessagePlugin.info('暂无新邮件')
    }
  }, 1000)
}

// 清空收信箱
const clearInbox = () => {
  emails.value = []
  selectedEmail.value = null
  MessagePlugin.success('收信箱已清空')
}

// 选择邮件
const selectEmail = (email) => {
  selectedEmail.value = email
}

// 格式化时间
const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return time.toLocaleDateString('zh-CN')
}

// 获取邮件预览
const getEmailPreview = (content) => {
  if (content.length <= 50) return content
  return content.substring(0, 50) + '...'
}

// 组件挂载时初始化数据
onMounted(() => {
  // 加载mock邮件数据
  emails.value = [...mockEmails]

  // 生成初始前缀
  generateRandomPrefix()
})
</script>

<style lang="less" scoped>
.get-mailbox-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 16px;

  @media (min-width: 640px) {
    padding: 60px 24px;
  }
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    font-size: 40px;
  }
}

.page-description {
  font-size: 18px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;

  @media (min-width: 1024px) {
    grid-template-columns: 400px 1fr;
  }
}

// 邮箱生成器样式
.mailbox-generator-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.generator-content {
  padding: 8px 0;
}

.section-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.domain-section {
  margin-bottom: 24px;
}

.domain-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.domain-option {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
  position: relative;

  &:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
  }

  .hot-tag {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.prefix-section {
  margin-bottom: 24px;
}

.prefix-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.prefix-input {
  flex: 1;
}

.random-btn {
  white-space: nowrap;
}

.email-preview {
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.preview-label {
  font-size: 14px;
  color: #6b7280;
  margin-right: 8px;
}

.preview-email {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.action-section {
  margin-top: 24px;
}

// 收信箱样式
.inbox-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.inbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inbox-actions {
  display: flex;
  gap: 8px;
}

.inbox-content {
  padding: 8px 0;
}

.empty-inbox {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
}

.email-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.email-item {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;

  &:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
  }

  &.active {
    border-color: #3b82f6;
    background-color: #eff6ff;
  }
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.email-sender {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.email-time {
  font-size: 12px;
  color: #6b7280;
}

.email-subject {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
  line-height: 1.4;
}

.email-preview {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8px;
}

.verification-code {
  margin-top: 8px;
}
</style>
