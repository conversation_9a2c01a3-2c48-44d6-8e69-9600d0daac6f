<template>
  <div class="get-mailbox-page">
    <div class="container">
      <div class="header-section">
        <h1 class="page-title">获取邮箱</h1>
        <p class="page-description">
          创建您的专属无限邮箱，享受无限容量和高级功能
        </p>
      </div>

      <div class="content-section">
        <t-card class="mailbox-form-card">
          <template #header>
            <h2>创建新邮箱</h2>
          </template>
          
          <t-form
            :data="formData"
            :rules="rules"
            ref="formRef"
            @submit="handleSubmit"
          >
            <t-form-item label="邮箱用户名" name="username">
              <t-input
                v-model="formData.username"
                placeholder="请输入邮箱用户名"
                suffix="@mailcode.com"
              />
            </t-form-item>

            <t-form-item label="密码" name="password">
              <t-input
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
              />
            </t-form-item>

            <t-form-item label="确认密码" name="confirmPassword">
              <t-input
                v-model="formData.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
              />
            </t-form-item>

            <t-form-item label="邮箱类型" name="type">
              <t-radio-group v-model="formData.type">
                <t-radio value="personal">个人邮箱</t-radio>
                <t-radio value="business">企业邮箱</t-radio>
              </t-radio-group>
            </t-form-item>

            <t-form-item>
              <t-button
                theme="primary"
                type="submit"
                size="large"
                :loading="loading"
                block
              >
                创建邮箱
              </t-button>
            </t-form-item>
          </t-form>
        </t-card>

        <div class="features-preview">
          <h3>邮箱功能特色</h3>
          <div class="features-list">
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>无限存储空间</span>
            </div>
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>企业级安全保护</span>
            </div>
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>多设备同步</span>
            </div>
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>智能垃圾邮件过滤</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

const formData = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  type: 'personal'
})

const rules = {
  username: [
    { required: true, message: '请输入邮箱用户名' },
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含字母、数字、下划线和连字符' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 8, message: '密码长度至少8个字符' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (val) => {
        return val === formData.password
      },
      message: '两次输入的密码不一致'
    }
  ]
}

const handleSubmit = async ({ validateResult }) => {
  if (validateResult === true) {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 创建成功后跳转到我的邮箱页面
      router.push('/my-mailbox')
    } catch (error) {
      console.error('创建邮箱失败:', error)
    } finally {
      loading.value = false
    }
  }
}
</script>

<style lang="less" scoped>
.get-mailbox-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 16px;

  @media (min-width: 640px) {
    padding: 60px 24px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    font-size: 40px;
  }
}

.page-description {
  font-size: 18px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 300px;
  }
}

.mailbox-form-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.features-preview {
  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 20px;
  }
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #16a34a;
  font-weight: 500;

  span {
    color: #374151;
  }
}
</style>
