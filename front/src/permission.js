/**
 * 路由权限控制
 * 前置路由守卫和后置路由守卫
 */
import router from '@/router'
import { useUserStore } from '@/store'
import { MessagePlugin } from 'tdesign-vue-next'
import { createApp } from 'vue'
import PageLoading from '@/components/PageLoading.vue'

// 全屏加载状态
let loadingInstance = null
let loadingStartTime = null

// 白名单路由 - 不需要登录就可以访问的页面
const whiteList = ['/login', '/register', '/', '/get-mailbox', '/contact']

// 需要登录的路由
const authRequiredRoutes = ['/my-mailbox']

/**
 * 前置路由守卫
 */
router.beforeEach(async (to, from, next) => {
  // 开始全屏加载动画
  if (loadingInstance) {
    document.body.removeChild(loadingInstance)
    loadingInstance = null
  }

  loadingStartTime = Date.now()

  // 创建自定义加载组件
  const loadingDiv = document.createElement('div')
  loadingDiv.id = 'page-loading-container'
  document.body.appendChild(loadingDiv)

  const loadingApp = createApp(PageLoading, {
    visible: true,
    text: '页面加载中'
  })

  loadingInstance = loadingDiv
  loadingApp.mount(loadingDiv)

  const userStore = useUserStore()
  const isLoggedIn = userStore.isLoggedIn

  // 如果用户已登录
  if (isLoggedIn) {
    // 如果要访问登录页或注册页
    if (to.path === '/login' || to.path === '/register') {
      // 检查是否有重定向参数，如果有则跳转到重定向地址，否则跳转到我的邮箱
      const redirectPath = to.query.redirect || '/my-mailbox'
      next(decodeURIComponent(redirectPath))
    } else {
      // 正常访问
      next()
    }
  } else {
    // 用户未登录
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接访问
      next()
    } else if (authRequiredRoutes.some(route => to.path.startsWith(route))) {
      // 需要登录的路由，重定向到登录页并在URL中保存重定向路径
      await MessagePlugin.warning('请先登录')
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
    } else {
      // 其他路由，直接访问
      next()
    }
  }
})

/**
 * 后置路由守卫
 */
router.afterEach((to, from) => {
  // 结束全屏加载动画
  if (loadingInstance) {
    const loadingDuration = Date.now() - loadingStartTime
    const minLoadingTime = 500 // 最小显示时间500ms
    const additionalDelay = 300 // 额外延迟300ms确保页面渲染完成

    // 计算需要延迟的时间
    const remainingTime = Math.max(0, minLoadingTime - loadingDuration)
    const totalDelay = remainingTime + additionalDelay

    setTimeout(() => {
      if (loadingInstance) {
        document.body.removeChild(loadingInstance)
        loadingInstance = null
        loadingStartTime = null
      }
    }, totalDelay)
  }

  // 设置页面标题
  const defaultTitle = 'MailCode - 临时邮箱服务'
  const routeTitles = {
    '/': 'MailCode - 临时邮箱服务',
    '/login': '登录 - MailCode',
    '/register': '注册 - MailCode',
    '/get-mailbox': '获取邮箱 - MailCode',
    '/my-mailbox': '我的邮箱 - MailCode',
    '/contact': '联系我们 - MailCode'
  }

  document.title = routeTitles[to.path] || defaultTitle

  // 记录路由访问日志（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log(`路由跳转: ${from.path} -> ${to.path}`)
  }

  // 页面滚动到顶部
  window.scrollTo(0, 0)
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  // 立即关闭全屏加载动画
  if (loadingInstance) {
    document.body.removeChild(loadingInstance)
    loadingInstance = null
    loadingStartTime = null
  }

  console.error('路由错误:', error)
  MessagePlugin.error('页面加载失败，请刷新重试')
})

/**
 * 获取登录后应该重定向的路径
 */
export const getRedirectPath = () => {
  // 从 URL 参数获取重定向路径
  const urlParams = new URLSearchParams(window.location.search)
  const redirectParam = urlParams.get('redirect')
  if (redirectParam) {
    return decodeURIComponent(redirectParam)
  }

  // 默认返回我的邮箱
  return '/my-mailbox'
}

/**
 * 检查路径是否安全（防止重定向到外部网站）
 */
export const isSafePath = (path) => {
  // 检查是否为相对路径
  if (!path || !path.startsWith('/')) {
    return false
  }

  // 检查是否包含危险字符
  const dangerousPatterns = ['//', 'javascript:', 'data:', 'vbscript:']
  return !dangerousPatterns.some(pattern => path.toLowerCase().includes(pattern))
}

export default router
